package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.audit.entity.PortfolioNetValueWarningParam;
import cn.sdata.om.al.audit.enums.WarningStatus;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.job.audit.PortfolioNetValueWarningMailJob;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.MarketTradeDayService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.quartz.JobDataMap;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.audit.entity.PortfolioNetValueWarning;
import cn.sdata.om.al.mapper.audit.PortfolioNetValueWarningMapper;
import cn.sdata.om.al.mapper.ValuationDBMapper;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.entity.AccountInformation;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.LinkedHashMap;

import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.*;
import static cn.sdata.om.al.qrtz.constant.CronConstant.SYNC;

@Service
@AllArgsConstructor
public class PortfolioNetValueWarningService extends ServiceImpl<PortfolioNetValueWarningMapper, PortfolioNetValueWarning> {

    private final CronService cronService;
    private final MarketTradeDayService marketTradeDayService;
    private final ValuationDBMapper valuationDBMapper;
    private final AccountInformationService accountInformationService;

    public void sendMail(@NonNull PortfolioNetValueWarningParam portfolioNetValueWarningParam) {
        List<String> productIds = portfolioNetValueWarningParam.getProductIds();

        // 获取当前时间
        LocalTime currentTime = LocalTime.now();
        LocalTime cutoffTime = LocalTime.of(15, 0); // 15:00

        // 根据当前时间确定dataDate和valuationTime
        String dataDate;
        String valuationTime;

        if (currentTime.isBefore(cutoffTime)) {
            // 15:00之前：dataDate为T-1日，valuationTime为T1
            dataDate = marketTradeDayService.getNetValueTradeDay(null, "00", -1);
            valuationTime = "T1";
        } else {
            // 15:00之后：dataDate为T日，valuationTime为T0
            dataDate = marketTradeDayService.getNetValueTradeDay(null, "00", 0);
            valuationTime = "T0";
        }

        // 如果没有指定产品ID，则查询指定日期所有有预警的产品
        if (productIds == null || productIds.isEmpty()) {
            LambdaQueryWrapper<PortfolioNetValueWarning> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(PortfolioNetValueWarning::getDataDate, dataDate);
            lambdaQueryWrapper.eq(PortfolioNetValueWarning::getValuationTime, valuationTime);
            lambdaQueryWrapper.ne(PortfolioNetValueWarning::getStatusMessage, WarningStatus.NONE);
            productIds = this.list(lambdaQueryWrapper).stream().map(PortfolioNetValueWarning::getProductId).collect(Collectors.toList());
        }

        List<String> jobIds = cronService.getJobIdByClass(PortfolioNetValueWarningMailJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(DATA_DATE, dataDate);
        jobDataMap.put(PRODUCT_ID, productIds);
        jobDataMap.put(REMOTE_FILE, new LinkedHashMap<String, RemoteFileInfo>());
        jobDataMap.put(SYNC, true);
        cronService.startJobNow(jobIds, jobDataMap);
    }

    /**
     * 处理指定日期的净值预警逻辑（与Job一致）
     */
    public void processWarningForDate(String date) {
        List<String> productIds = accountInformationService.list().stream()
                .filter(accountInformation -> accountInformation.getProductCategory() == 3)
                .map(AccountInformation::getProductCode).collect(Collectors.toList());
        List<PortfolioNetValueWarning> portfolioNetValueWarnings = valuationDBMapper.getPortfolioNetValueWarning(date, productIds);
        Map<String, AccountInformation> collect = accountInformationService.list().stream()
                .collect(Collectors.toMap(AccountInformation::getProductCode,
                        accountInformation -> accountInformation,
                        (oldOne, newOne) -> newOne));
        for (PortfolioNetValueWarning portfolioNetValueWarning : portfolioNetValueWarnings) {
            String productCode = portfolioNetValueWarning.getProductCode();
            AccountInformation accountInformation = collect.get(productCode);
            if (accountInformation == null) {
                continue;
            }
            portfolioNetValueWarning.setProductId(accountInformation.getId());
            portfolioNetValueWarning.setProductCode(productCode);
            portfolioNetValueWarning.setValuationTime(accountInformation.getValuationTime());
            String warnValue = accountInformation.getWarnValue();
            String closeValue = accountInformation.getCloseValue();
            portfolioNetValueWarning.setWarningValue(safeCreate(warnValue));
            portfolioNetValueWarning.setLiquidationValue(safeCreate(closeValue));
            portfolioNetValueWarning.setForfeitureValue(safeCreate(closeValue));
            BigDecimal warningValue = portfolioNetValueWarning.getWarningValue();
            BigDecimal warningRange = portfolioNetValueWarning.getWarningRange();
            BigDecimal liquidationValue = portfolioNetValueWarning.getLiquidationValue();
            BigDecimal netValue = portfolioNetValueWarning.getNetValue();
            BigDecimal fundShare = portfolioNetValueWarning.getFundShare();
            boolean isLiquidation = false;
            BigDecimal realWarningRange = null;
            if (netValue == null) {
                continue;
            }
            if (warningValue != null && warningRange != null) {
                BigDecimal subtract = netValue.subtract(warningValue);
                realWarningRange = subtract.divide(warningValue, 4, RoundingMode.HALF_UP);
                if (realWarningRange.compareTo(warningRange) < 0) {
                    portfolioNetValueWarning.setStatusMessage(cn.sdata.om.al.audit.enums.WarningStatus.NEAR_WARNING);
                }
                if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                    portfolioNetValueWarning.setWarningTriggered(1);
                    portfolioNetValueWarning.setStatusMessage(cn.sdata.om.al.audit.enums.WarningStatus.WARNING_TRIGGERED);
                }
            }
            if (liquidationValue != null) {
                BigDecimal subtract = netValue.subtract(liquidationValue);
                BigDecimal realLiquidationRange = subtract.divide(liquidationValue, 4, RoundingMode.HALF_UP);
                if (realLiquidationRange.compareTo(warningRange) < 0) {
                    portfolioNetValueWarning.setWarningTriggered(1);
                    portfolioNetValueWarning.setStatusMessage(cn.sdata.om.al.audit.enums.WarningStatus.NEAR_LIQUIDATION);
                }
                if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                    portfolioNetValueWarning.setWarningTriggered(1);
                    isLiquidation = true;
                    portfolioNetValueWarning.setStatusMessage(cn.sdata.om.al.audit.enums.WarningStatus.LIQUIDATION_TRIGGERED);
                }
            }
            if(fundShare != null && realWarningRange != null && portfolioNetValueWarning.getWarningTriggered() == 1 && !isLiquidation) {
                portfolioNetValueWarning.setRequiredFunding(netValue.subtract(warningValue).multiply(fundShare).abs());
            }
        }
        PortfolioNetValueWarningService portfolioNetValueWarningService = (PortfolioNetValueWarningService) AopContext.currentProxy();
        portfolioNetValueWarningService.saveOrUpdate(date, portfolioNetValueWarnings);
    }

    @Transactional
    public void saveOrUpdate(String date, List<PortfolioNetValueWarning> portfolioNetValueWarnings){
        LambdaQueryWrapper<PortfolioNetValueWarning> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PortfolioNetValueWarning::getDataDate, date);
        this.remove(lambdaQueryWrapper);
        this.saveBatch(portfolioNetValueWarnings);
    }

    /**
     * 区间批量处理
     */
    public void processWarningForDateRange(String startDate, String endDate) {
        java.util.Date start = java.sql.Date.valueOf(startDate);
        java.util.Date end = java.sql.Date.valueOf(endDate);
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(start);
        while (!cal.getTime().after(end)) {
            String date = new java.text.SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
            processWarningForDate(date);
            cal.add(java.util.Calendar.DATE, 1);
        }
    }

    private BigDecimal safeCreate(Object value) {
        if (value == null) {
            return null;
        }
        try {
            return new BigDecimal(value.toString().trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取指定偏移的交易日（T-1或T）
     */
    public String getMarketTradeDay(int offset) {
        return marketTradeDayService.getNetValueTradeDay(null, "00", offset);
    }
}
