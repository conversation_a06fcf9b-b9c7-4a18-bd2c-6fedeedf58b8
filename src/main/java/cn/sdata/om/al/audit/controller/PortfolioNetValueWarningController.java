package cn.sdata.om.al.audit.controller;

import cn.sdata.om.al.audit.entity.PortfolioNetValueWarning;
import cn.sdata.om.al.audit.entity.PortfolioNetValueWarningParam;
import cn.sdata.om.al.audit.enums.WarningStatus;
import cn.sdata.om.al.audit.service.PortfolioNetValueWarningService;
import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.CommonQueryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("audit/portfolio")
@AllArgsConstructor
public class PortfolioNetValueWarningController {

    private final PortfolioNetValueWarningService portfolioNetValueWarningService;
    private final CommonQueryService<PortfolioNetValueWarning> commonQueryService;

    /**
     * Get all portfolio net value warnings
     *
     * @return list of warnings
     */
    @PostMapping("page")
    public R<Page<PortfolioNetValueWarning>> page(@RequestBody CommonPageParam<PortfolioNetValueWarning> commonPageParam) {
        Page<PortfolioNetValueWarning> page = new Page<>();
        page.setSize(commonPageParam.getSize());
        page.setCurrent(commonPageParam.getCurrent());
        Map<String, String> orderColumn = commonPageParam.getOrderColumn();
        if (orderColumn == null) {
            orderColumn = new HashMap<>();
        }
        orderColumn.put("status_message", "desc");
        commonPageParam.setOrderColumn(orderColumn);
        Page<PortfolioNetValueWarning> portfolioNetValueWarningPage = commonQueryService.commonPage(commonPageParam, PortfolioNetValueWarning.class);
        return R.ok(portfolioNetValueWarningPage);
    }

    /**
     * 发送预警邮件
     * 系统会根据当前时间自动确定数据日期和估值时间：
     * - 15:00之前：dataDate为T-1日，valuationTime为T1
     * - 15:00之后：dataDate为T日，valuationTime为T0
     *
     * @param portfolioNetValueWarningParam 包含产品ID列表的对象
     * @return 发送结果
     */
    @PostMapping("mail")
    public R<?> sendMail(@RequestBody PortfolioNetValueWarningParam portfolioNetValueWarningParam){
        portfolioNetValueWarningService.sendMail(portfolioNetValueWarningParam);
        return R.ok("发送成功");
    }

    /**
     * 组合产品净值预警数量
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @return 预警数量
     */
    @GetMapping("count")
    public R<Long> getWarningCountByDateRange(@RequestParam("startDate") String startDate,
                                             @RequestParam("endDate") String endDate,
                                             @RequestParam("valuationTime") String valuationTime) {
        LambdaQueryWrapper<PortfolioNetValueWarning> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(PortfolioNetValueWarning::getDataDate, startDate);
        queryWrapper.le(PortfolioNetValueWarning::getDataDate, endDate);
        queryWrapper.ne(PortfolioNetValueWarning::getStatusMessage, WarningStatus.NONE);
        if (valuationTime != null) {
            queryWrapper.eq(PortfolioNetValueWarning::getValuationTime, valuationTime);
        }
        long count = portfolioNetValueWarningService.count(queryWrapper);
        return R.ok(count);
    }

    /**
     * 区间处理净值预警
     */
    @PostMapping("/process-range")
    public R<String> syncRange(@RequestBody Map<String, String> param) {
        String startDate = param.get("startDate");
        String endDate = param.get("endDate");
        try {
            if ((startDate == null || startDate.isEmpty()) && (endDate == null || endDate.isEmpty())) {
                java.time.LocalTime currentTime = java.time.LocalTime.now();
                java.time.LocalTime cutoffTime = java.time.LocalTime.of(15, 0);
                String dataDate;
                if (currentTime.isBefore(cutoffTime)) {
                    // 15:00之前：T-1
                    dataDate = portfolioNetValueWarningService.getMarketTradeDay(-1);
                } else {
                    // 15:00之后：T
                    dataDate = portfolioNetValueWarningService.getMarketTradeDay(0);
                }
                portfolioNetValueWarningService.processWarningForDate(dataDate);
            } else {
                portfolioNetValueWarningService.processWarningForDateRange(startDate, endDate);
            }
            return R.ok("同步成功");
        } catch (Exception e) {
            return R.ok("同步失败");
        }
    }

}
