package cn.sdata.om.al.audit.controller;

import cn.sdata.om.al.audit.dto.PortfolioFluctuationRangeDTO;
import cn.sdata.om.al.audit.enums.CompareType;
import cn.sdata.om.al.audit.service.PortfolioFluctuationRangeService;
import cn.sdata.om.al.result.R;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.HashMap;

/**
 * 组合净值波动范围规则Controller
 */
@RestController
@RequestMapping("/audit/portfolio-fluctuation-range")
@AllArgsConstructor
public class PortfolioFluctuationRangeController {

    private final PortfolioFluctuationRangeService portfolioFluctuationRangeService;

    /**
     * 获取所有比较类型选项
     * @return 比较类型列表
     */
    @GetMapping("/compare-types")
    public R<List<Map<String, Object>>> getCompareTypes() {
        List<Map<String, Object>> compareTypes = Stream.of(CompareType.values())
                .map(type -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("ordinal", type.ordinal());
                    map.put("value", type.getSymbol());
                    map.put("label", type.getDescription());
                    return map;
                })
                .collect(Collectors.toList());
        return R.ok(compareTypes);
    }

    /**
     * 获取所有波动范围规则
     * @return 波动范围规则列表
     */
    @GetMapping("/list")
    public R<List<PortfolioFluctuationRangeDTO>> listAllRanges() {
        List<PortfolioFluctuationRangeDTO> ranges = portfolioFluctuationRangeService.listAllRanges();
        return R.ok(ranges);
    }

    /**
     * 保存单个波动范围规则
     * @param rangeDTO 波动范围规则DTO
     * @return 是否保存成功
     */
    @PostMapping("/save")
    public R<Boolean> saveFluctuationRange(@RequestBody PortfolioFluctuationRangeDTO rangeDTO) {
        boolean result = portfolioFluctuationRangeService.saveFluctuationRange(rangeDTO);
        return R.ok(result);
    }

    /**
     * 批量保存波动范围规则（整体替换）
     * @param rangeDTOList 波动范围规则DTO列表
     * @return 是否保存成功
     */
    @PostMapping("/batch-save")
    public R<Boolean> saveFluctuationRanges(@RequestBody List<PortfolioFluctuationRangeDTO> rangeDTOList) {
        boolean result = portfolioFluctuationRangeService.saveFluctuationRanges(rangeDTOList);
        return result? R.ok("保存成功") : R.failed("保存失败");
    }

    /**
     * 验证波动率是否在指定范围内
     * @param fluctuationRate 波动率
     * @param productId 产品ID
     * @return 是否在范围内
     */
    @GetMapping("/validate")
    public R<Boolean> validateFluctuationRate(@RequestParam("fluctuationRate") BigDecimal fluctuationRate,
                                             @RequestParam("productId") String productId) {
        boolean isValid = portfolioFluctuationRangeService.validateFluctuationRate(fluctuationRate, productId);
        return R.ok(isValid);
    }
} 