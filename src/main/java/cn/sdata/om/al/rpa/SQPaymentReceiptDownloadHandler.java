package cn.sdata.om.al.rpa;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.service.InterbankFeesService;
import cn.sdata.om.al.service.RemoteFileInfoService;
import cn.sdata.om.al.utils.LogFYBUtils;
import cn.sdata.om.al.utils.OmFileUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

@Slf4j
@Component
public class SQPaymentReceiptDownloadHandler implements BaseHandler {

    private RemoteFileInfoService remoteFileInfoService;

    @Autowired
    public void setRemoteFileInfoService(RemoteFileInfoService remoteFileInfoService) {
        this.remoteFileInfoService = remoteFileInfoService;
    }

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        log.info("开始处理上清缴费单下载文件...");
        String logId = String.valueOf(param.get(RPAConstant.LOG_ID)),
                dataDate = String.valueOf(param.get(BaseConstant.SYSTEM_DATE_NAME)),
                executor = String.valueOf(param.get(CronConstant.EXECUTOR));
        try {
            Assert.notNull(param, "param不能为空");
            Assert.notNull(files, "files不能为空");
            Cron cron = (Cron) param.get(RPAConstant.CRON);
            log.info("SQPaymentReceiptDownloadHandler_execute_logId:{},dataDate:{},executor:{}", logId, dataDate, executor);
            Assert.notNull(cron, "cron不能为空");
            Assert.notNull(logId, "logId不能为空");
            Assert.notNull(dataDate, "dataDate不能为空");
            files.forEach(remoteFileInfo -> remoteFileInfo.setLogId(logId));
            remoteFileInfoService.saveBatch(files);
            if (CollectionUtil.isNotEmpty(files)) {
                List<File> fileList = OmFileUtil.transformToFiles(files);
                if (CollectionUtil.isNotEmpty(fileList)) {
                    for (File file : fileList) {
                        log.info("处理的文件名为:{}", file.getAbsolutePath());
                        String extName = FileUtil.extName(file);
                        if ("zip".equalsIgnoreCase(extName)) {
                            log.info("此文件为压缩包需要解压");
                            File unzip = ZipUtil.unzip(file);
                            List<File> loopFiles = FileUtil.loopFiles(unzip);
                            if (CollectionUtil.isNotEmpty(loopFiles)) {
                                List<MultipartFile> multipartFiles = new ArrayList<>();
                                for (File loopFile : loopFiles) {
                                    MultipartFile multipartFile = OmFileUtil.fileToMultipartFile(loopFile);
                                    multipartFiles.add(multipartFile);
                                }
                                if (CollectionUtil.isNotEmpty(multipartFiles)) {
                                    SpringUtil.getBean(InterbankFeesService.class).upload(multipartFiles.toArray(MultipartFile[]::new), null, DateUtil.today());
                                }
                            }
                        }
                    }
                }
            }
            JSONObject params = new JSONObject();
            params.put("rpaLogId", logId);
            params.put("files", files);
            params.put("fileType", "上清");
            params.put("status", CommonStatus.SUCCESS.name());
            params.put("endTime", DateUtil.now());
            LogFYBUtils.postRpaLog(params);
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject params = new JSONObject();
            params.put("rpaLogId", logId);
            params.put("fileType", "上清");
            params.put("status", CommonStatus.FAIL.name());
            params.put("exception", e.getMessage());
            params.put("endTime", DateUtil.now());
            LogFYBUtils.postRpaLog(params);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void onFail(Map<String, Object> param) {
        Object o = param.get(LOG_ID);
        if (o != null) {
            Object errorMsg = param.get("errorMsg");
            String rpaLogId = String.valueOf(o);
            JSONObject params = new JSONObject();
            params.put("rpaLogId", rpaLogId);
            params.put("exception", errorMsg != null ? String.valueOf(errorMsg) : "");
            params.put("endTime", DateUtil.now());
            params.put("status", CommonStatus.FAIL.name());
            params.put("fileType", "上清");
            LogFYBUtils.postRpaLog(params);
        }
    }
}
