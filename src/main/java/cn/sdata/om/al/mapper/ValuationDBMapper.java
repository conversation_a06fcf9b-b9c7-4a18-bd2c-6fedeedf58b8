package cn.sdata.om.al.mapper;

import cn.sdata.om.al.audit.entity.PortfolioNetValueFluctuation;
import cn.sdata.om.al.audit.entity.PortfolioNetValueWarning;
import cn.sdata.om.al.entity.SecondValuationData;
import cn.sdata.om.al.entity.ValuationTableData;
import cn.sdata.om.al.entity.ValuationTableStatus;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
@DS("valuation")
public interface ValuationDBMapper {

    List<ValuationTableStatus> getGenerateStatus(@Param("date") String date);

    List<ValuationTableStatus> getReconciliationStatus(@Param("date") String date);

    List<ValuationTableStatus> getConfirmStatus(@Param("date") String date);

    List<ValuationTableData> getStructuralTableData(@Param("date") String date, @Param("productIds") List<String> productIds);

    List<ValuationTableData> getFlatTableData(@Param("date") String date, @Param("productIds") List<String> productIds);

    List<SecondValuationData> getSecondValuationData(@Param("subjectCodes") Collection<String> subjectCodes, @Param("date") String date, @Param("valuationTime") String valuationTime);

    List<PortfolioNetValueWarning> getPortfolioNetValueWarning(@Param("date") String date, @Param("productIds") List<String> productIds);

    List<PortfolioNetValueFluctuation> getPortfolioNetValueFluctuation(@Param("date") String date, @Param("productIds") List<String> productIds);




}
