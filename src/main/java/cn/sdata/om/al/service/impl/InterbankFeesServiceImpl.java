package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.*;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.job.InterbankFeesJob;
import cn.sdata.om.al.mapper.*;
import cn.sdata.om.al.ocr.OCRResult;
import cn.sdata.om.al.ocr.OCRUtil;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.InterbankFeesService;
import cn.sdata.om.al.service.MarketTradeDayService;
import cn.sdata.om.al.service.RpaExecuteService;
import cn.sdata.om.al.service.RpaService;
import cn.sdata.om.al.utils.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.Month;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_USERNAME;

@Service
@Slf4j
public class InterbankFeesServiceImpl implements InterbankFeesService {

    private InterbankFeesMapper interbankFeesMapper;

    private OCRUtil ocrUtil;

    private O32AssetUnitMapper o32AssetUnitMapper;

    @Value("${inter-bank-fees.flow-ids}")
    private String flowIds;

    @Value("${file.inter-bank-fees-dir}")
    private String filePath;

    @Value("${file.import-o32-dir}")
    private String importO32FilePath;

    private SMBManager smbManager;

    private static volatile boolean isUpload = false;

    private static volatile boolean isImportO32 = false;

    private static volatile boolean isSyncPayStatus;

    @Autowired
    public void setInterbankFeesMapper(InterbankFeesMapper interbankFeesMapper) {
        this.interbankFeesMapper = interbankFeesMapper;
    }

    @Autowired
    public void setOcrUtil(OCRUtil ocrUtil) {
        this.ocrUtil = ocrUtil;
    }

    @Autowired
    public void setO32AssetUnitMapper(O32AssetUnitMapper o32AssetUnitMapper) {
        this.o32AssetUnitMapper = o32AssetUnitMapper;
    }

    @Autowired
    public void setSmbManager(SMBManager smbManager) {
        this.smbManager = smbManager;
    }

    @Override
    public PageInfo<InterBankFees> page(InterbankFeesQuery interbankFeesQuery) {
        int pageNo = interbankFeesQuery.getPageNo();
        int pageSize = interbankFeesQuery.getPageSize();
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<InterBankFees> interbankFees = interbankFeesMapper.page(interbankFeesQuery);
            if (CollectionUtil.isNotEmpty(interbankFees)) {
                for (InterBankFees fees : interbankFees) {
                    String amount = fees.getAmount();
                    if (StringUtils.isNotBlank(amount)) {
                        fees.setAmount(CommonUtil.handleNumberStr(amount));
                    }
                }
            }
            return new PageInfo<>(interbankFees);
        }
    }

    @Override
    public String upload(MultipartFile[] files, String mailId, String noticeDate) {
        if (!isUpload) {
            List<InterBankFeesFile> interBankFeesFiles = new ArrayList<>();
            List<JSONObject> jsonObjects = new ArrayList<>();
            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
                    try {
                        String fileId = IdUtil.getSnowflakeNextIdStr();
                        InterBankFeesFile interBankFeesFile = new InterBankFeesFile();
                        interBankFeesFile.setId(fileId);
                        interBankFeesFile.setFileName(file.getOriginalFilename());
                        File uploadFile = new File(filePath + file.getOriginalFilename());
                        file.transferTo(uploadFile);
                        interBankFeesFile.setFilePath(uploadFile.getAbsolutePath());
                        interBankFeesFiles.add(interBankFeesFile);
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("fileId", fileId);
                        jsonObject.put("file", uploadFile);
                        jsonObjects.add(jsonObject);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            CompletableFuture.runAsync(() -> {
                isUpload = true;
                log.info("开始银行间缴费信息录入...");
                if (ArrayUtil.isNotEmpty(files)) {
                    List<InterBankFees> res = new ArrayList<>();
                    List<AccountInformation> accountInformationList = SpringUtil.getBean(AccountInformationMapper.class).selectList(null);
                    for (JSONObject obj : jsonObjects) {
                        try {
                            File file = obj.getObject("file", File.class);
                            String fileId = obj.getString("fileId");
                            OCRResult ocrResult = ocrUtil.executeOCR(FileUtil.readBytes(file));
                            List<InterBankFees> interbankFees = generateInterbankFees(ocrResult, accountInformationList);
                            if (CollectionUtil.isNotEmpty(interbankFees)) {
                                interbankFees = interbankFees.stream().peek(n -> {
                                    n.setFileId(fileId);
                                    n.setMailId(mailId);
                                    n.setNoticeDate(noticeDate);
                                }).collect(Collectors.toList());
                                Iterator<InterBankFees> iterator = interbankFees.iterator();
                                while (iterator.hasNext()) {
                                    InterBankFees interBankFees = iterator.next();
                                    int count = interbankFeesMapper.selectRepeatData(interBankFees);
                                    if (count > 0) {
                                        // 重复数据不重复添加
                                        iterator.remove();
                                    }
                                }
                                if (CollectionUtil.isNotEmpty(interbankFees)) {
                                    res.addAll(interbankFees);
                                }
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage());
                        }
                    }
                    try {
                        if (CollectionUtil.isNotEmpty(interBankFeesFiles)) {
                            interbankFeesMapper.batchSaveFile(interBankFeesFiles);
                        }
                        if (CollectionUtil.isNotEmpty(res)) {
                            interbankFeesMapper.batchSave(res);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                isUpload = false;
                log.info("银行间缴费信息录入结束...");
            });
        }
        return isUpload ? "executing" : "completed";
    }

    @Override
    public InterBankFees getById(String id) {
        InterBankFees interbankFees = interbankFeesMapper.getById(id);
        if (interbankFees != null) {
            String fileId = interbankFees.getFileId();
            if (StringUtils.isNotBlank(fileId)) {
                InterBankFeesFile interBankFeesFile = interbankFeesMapper.getFileById(fileId);
                if (interBankFeesFile != null) {
                    String filePath = interBankFeesFile.getFilePath();
                    if (StringUtils.isNotBlank(filePath)) {
                        File file = new File(filePath);
                        try {
                            /*PDDocument document = PDDocument.load(file);
                            PDFRenderer renderer = new PDFRenderer(document);
                            BufferedImage image = renderer.renderImageWithDPI(0, 100);
                            ByteArrayOutputStream baos = new ByteArrayOutputStream();
                            ImageIO.write(image, "png", baos);
                            byte[] bytes = baos.toByteArray();*/
                            String base64String = cn.hutool.core.codec.Base64.encode((FileUtil.readBytes(file)));
                            interbankFees.setBase64FileStr(base64String);
                            // document.close();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
        return interbankFees;
    }

    @Override
    public boolean update(InterBankFees interbankFees) {
        String id = interbankFees.getId();
        if (StringUtils.isNotBlank(id)) {
            JSONObject params = new JSONObject();
            if (StringUtils.isNotBlank(interbankFees.getPaymentStatus())) {
                InterBankFees interBankFees = interbankFeesMapper.getById(id);
                params.put("logId", IdUtil.getSnowflakeNextIdStr());
                params.put("username", SecureUtil.currentUserName());
                params.put("beginTime", DateUtil.now());
                if (interBankFees != null) {
                    String paymentStatus = interBankFees.getPaymentStatus();
                    String beginCostDate = interBankFees.getBeginCostDate();
                    params.put("preUpdateStatus", paymentStatus);
                    params.put("productId", interBankFees.getProductId());
                    params.put("params", JSON.toJSONString(interbankFees));
                    params.put("dataDate", beginCostDate);
                }
                LogFYBUtils.preUpdatePayStatusLog(params);
                interbankFees.setPaymentStatusUpdateTime(DateUtils.now());
            }
            String amount = interbankFees.getAmount();
            if (StringUtils.isNotBlank(amount)) {
                interbankFees.setAmount(amount.replaceAll(",", ""));
            }
            interbankFees.setPaymentStatus(interbankFees.getPaymentStatus());
            interbankFees.setUpdateTime(DateUtil.now());
            interbankFees.setUpdateByName(SecureUtil.currentUserName());
            interbankFees.setOcrRecognizeStatus(OcrRecognizeStatusEnum.CONFIRMED.name());
            interbankFees.setProductId(interbankFees.getProductId());
            int res = interbankFeesMapper.update(interbankFees);
            if (StringUtils.isNotBlank(interbankFees.getPaymentStatus())) {
                if (StringUtils.isNotBlank(params.getString("logId"))) {
                    params.put("endTime", DateUtil.now());
                    params.put("postUpdateStatus", interbankFees.getPaymentStatus());
                    LogFYBUtils.postUpdatePayStatusLog(params);
                }
            }
            return res > 0;
        }
        return false;
    }

    @Override
    public List<InterBankFeesFile> getFileByIds(List<String> ids) {
        return interbankFeesMapper.getFileByIds(ids);
    }

    @Override
    public String importO32(List<String> ids) {
        String username = SecureUtil.currentUserName();
        if (!isImportO32) {
            CompletableFuture.runAsync(() -> {
                isImportO32 = true;
                String initDate = null;
                // 查询业务日切时间
                try {
                    List<JSONObject> jsonObjects = interbankFeesMapper.testSelectO32Date();
                    if (CollectionUtil.isNotEmpty(jsonObjects)) {
                        JSONObject object = jsonObjects.get(0);
                        initDate = object.getString("L_INIT_DATE");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtils.isBlank(initDate)) {
                    initDate = DateUtil.format(new Date(), "yyyyMMdd");
                }
                List<InterBankFees> interbankFees = interbankFeesMapper.selectList(ids);
                if (CollectionUtil.isEmpty(interbankFees)) {
                    BusinessException.throwException("没有获取到费用信息");
                }
                // 生成o32文件
                List<FileAndIds> o32Files = generateO32File(interbankFees, 0, initDate);
                // 上传至共享文件夹
                if (CollectionUtil.isEmpty(o32Files)) {
                    BusinessException.throwException("没有生成任何o32文件");
                }
                uploadShareFolder(o32Files, 0, true);
                isImportO32 = false;
            });
        }
        return isImportO32 ? "executing" : "completed";
    }

    @Override
    public boolean uploadShareFolder(List<FileAndIds> o32Files, int flag, boolean executeTask) {
        try {
            String[] split = flowIds.split(",");
            String flowId;
            for (FileAndIds fileAndIds : o32Files) {
                JSONObject params = new JSONObject();
                try {
                    params.put("logId", IdUtil.getSnowflakeNextIdStr());
                    params.put("username", DEFAULT_USERNAME);
                    params.put("beginTime", DateUtil.now());
                    params.put("status", CommonStatus.EXECUTING.name());
                    params.put("params", JSON.toJSONString(fileAndIds.getIds()));
                    if (flag == 0) {
                        LogFYBUtils.preImportO32Log(params);
                    } else {
                        LogFYIUtils.preImportO32Log(params);
                    }
                    File file = fileAndIds.getFile();
                    String name = FileUtil.getName(file);
                    log.info("上传至共享文件夹文件名为:{}", name);
                    String dirPath;
                    String type;
                    if (name.contains("NONINVEST")) {
                        dirPath = "非投资类业务\\" + DateUtil.format(new Date(), "yyyyMMdd") + "\\";
                        flowId = split[1];
                        type = "NONINVEST";
                    } else {
                        dirPath = "资金管理\\" + DateUtil.format(new Date(), "yyyyMMdd") + "\\";
                        flowId = split[0];
                        type = "ASSET";
                    }
                    log.info("需要上传到共享文件夹的路径是:{}. 开始上传到共享文件夹...", dirPath);
                    smbManager.uploadFile(dirPath, name, FileUtil.readBytes(file));
                    if (executeTask) {
                        log.info("开始执行rpa任务导入O32... 执行的flowId = {}", flowId);
                        List<String> ids = fileAndIds.getIds();
                        if (CollectionUtil.isNotEmpty(ids)) {
                            executeRPA(flowId, ids, flag, dirPath, fileAndIds.getUploadOrder(), type);
                        }
                    }
                    if (flag == 0) {
                        String fybImportO32FilePath = SpringUtil.getProperty("file.fyb-import-file-path");
                        File dest = new File(fybImportO32FilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                        FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                        params.put("endTime", DateUtil.now());
                        params.put("fileUrl", dest.getAbsolutePath());
                        params.put("status", CommonStatus.SUCCESS.name());
                        List<InterBankFees> interbankFees = interbankFeesMapper.selectList(fileAndIds.getIds());
                        if (CollectionUtil.isNotEmpty(interbankFees)) {
                            List<JSONObject> result = interbankFees.stream().map(n -> {
                                JSONObject object = new JSONObject();
                                object.put("productId", n.getProductId());
                                object.put("importO32Status", n.getImportO32Status());
                                object.put("o32Message", n.getO32Message());
                                return object;
                            }).collect(Collectors.toList());
                            params.put("o32Result", JSON.toJSONString(result));
                        }
                        LogFYBUtils.postImportO32Log(params);
                    } else {
                        String fyiImportO32FilePath = SpringUtil.getProperty("file.fyi-import-file-path");
                        File dest = new File(fyiImportO32FilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                        FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                        params.put("endTime", DateUtil.now());
                        params.put("fileUrl", dest.getAbsolutePath());
                        params.put("status", CommonStatus.SUCCESS.name());
                        List<InsuranceRegistrationFees> insuranceRegistrationFees = SpringUtil.getBean(InsuranceRegistrationFeesMapper.class).selectList(fileAndIds.getIds());
                        if (CollectionUtil.isNotEmpty(insuranceRegistrationFees)) {
                            List<JSONObject> result = insuranceRegistrationFees.stream().map(n -> {
                                JSONObject object = new JSONObject();
                                object.put("productId", n.getProductId());
                                object.put("importO32Status", n.getImportO32Status());
                                object.put("o32Message", n.getO32Message());
                                return object;
                            }).collect(Collectors.toList());
                            params.put("o32Result", JSON.toJSONString(result));
                        }
                        LogFYIUtils.postImportO32Log(params);
                    }
                } catch (Exception e) {
                    params.put("endTime", DateUtil.now());
                    params.put("status", CommonStatus.FAIL.name());
                    params.put("errorMsg", e.getMessage());
                    if (flag == 0) {
                        LogFYBUtils.postImportO32Log(params);
                    } else {
                        LogFYIUtils.postImportO32Log(params);
                    }
                    e.printStackTrace();
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 同步支付状态
     * @param ids ids
     * @param type 类型 0 银行间 1 中保登
     * @return 状态
     */
    @Override
    public String syncPaymentStatus(List<String> ids, int type) {
        if (!isSyncPayStatus) {
            String username = SecureUtil.currentUserName();
            CompletableFuture.runAsync(() -> {
                isSyncPayStatus = true;
                JSONObject params = new JSONObject();
                params.put("logId", IdUtil.getSnowflakeNextIdStr());
                params.put("beginTime", DateUtil.now());
                params.put("username", username);
                params.put("params", JSON.toJSONString(ids));
                params.put("status", CommonStatus.EXECUTING.name());
                List<InterBankFees> interBankFees = new ArrayList<>();
                if (type == 1) {
                    InsuranceRegistrationFeesMapper insuranceRegistrationFeesMapper = SpringUtil.getBean(InsuranceRegistrationFeesMapper.class);
                    List<InsuranceRegistrationFees> insuranceRegistrationFees = insuranceRegistrationFeesMapper.selectList(ids);
                    if (CollectionUtil.isNotEmpty(insuranceRegistrationFees)) {
                        interBankFees = transformInsuranceRegistrationFeesServiceToInterBankFees(insuranceRegistrationFees);
                    }
                } else {
                    interBankFees = interbankFeesMapper.selectList(ids);
                }
                List<JSONObject> productInfos = new ArrayList<>();
                try {
                    if (type == 1) {
                        generatePreSyncPayStatusLogParam(params, interBankFees, productInfos);
                        LogFYIUtils.preSyncPayStatusLog(params);
                    } else {
                        generatePreSyncPayStatusLogParam(params, interBankFees, productInfos);
                        LogFYBUtils.preSyncPayStatusLog(params);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (CollectionUtil.isNotEmpty(interBankFees)) {
                    for (InterBankFees interBankFee : interBankFees) {
                        try {
                            String nameOfPayee = interBankFee.getNameOfPayee();
                            String beneficiaryAccount = interBankFee.getBeneficiaryAccount();
                            String amount = interBankFee.getAmount();
                            String payMonth = interBankFee.getPayMonth();
                            String productId = interBankFee.getProductId();
                            if (StringUtils.isEmpty(payMonth)) {
                                log.error("同步缴费状态时,缺少cop查询交费日期: {}", JSON.toJSONString(interBankFee));
                                continue;
                            }
                            String beginCostDate;
                            String endCostDate;
                            DateTime monthDateTime = DateUtil.parseDate(payMonth + "-01");
                            DateTime beginMonth = DateUtil.beginOfMonth(monthDateTime);
                            DateTime endMonth = DateUtil.endOfMonth(monthDateTime);
                            beginCostDate = DateUtil.format(beginMonth, "yyyyMMdd");
                            endCostDate = DateUtil.format(endMonth, "yyyyMMdd");
                            List<JSONObject> curr = interbankFeesMapper.selectCopPayStatus(nameOfPayee, beneficiaryAccount, null, beginCostDate, endCostDate, productId);
                            log.info("查询出来的当前cop数据为:{} - 条件是: nameOfPayee = {}, beneficiaryAccount = {}, beginCostDate = {}, endCostDate = {}, productId = {}", JSON.toJSONString(curr), nameOfPayee, beneficiaryAccount, beginCostDate, endCostDate, productId);
                            List<JSONObject> history = interbankFeesMapper.selectCopHistoryPayStatus(nameOfPayee, beneficiaryAccount, null, beginCostDate, endCostDate, productId);
                            log.info("查询出来的历史cop数据为:{} - 条件是: nameOfPayee = {}, beneficiaryAccount = {}, beginCostDate = {}, endCostDate = {}, productId = {}", JSON.toJSONString(history), nameOfPayee, beneficiaryAccount, beginCostDate, endCostDate, productId);
                            List<JSONObject> res = new ArrayList<>();
                            if (CollectionUtil.isNotEmpty(curr)) {
                                res.addAll(curr);
                            }
                            if (CollectionUtil.isNotEmpty(history)) {
                                res.addAll(history);
                            }
                            if (CollectionUtil.isNotEmpty(res)) {
                                if (res.size() == 1) {
                                    // 正常讲一个季度有一笔
                                    JSONObject object = res.get(0);
                                    // 取金额
                                    String tradeBalance = object.getString("trade_balance");
                                    log.info("查询出cop的缴费金额为:{}", tradeBalance);
                                    log.info("ocr识别出的缴费金额为:{}", amount);
                                    if (StringUtils.isNotBlank(tradeBalance) && StringUtils.isNotBlank(amount)) {
                                        Double tradeBalanceDouble = Double.parseDouble(tradeBalance);
                                        Double amountDouble = Double.parseDouble(amount.replaceAll(",", ""));
                                        if (tradeBalanceDouble.equals(amountDouble)) {
                                            log.info("缴费记录有且金额匹配... 更新支付状态");
                                            String id = interBankFee.getId();
                                            String transferTime = object.getString("TRANSFER_TIME");
                                            String currDate = object.getString("curr_date");
                                            String finalTransferTime = DateUtils.transformDate(currDate, transferTime);
                                            if (type == 1) {
                                                SpringUtil.getBean(InsuranceRegistrationFeesMapper.class).updatePayStatus(id, finalTransferTime, DateUtil.now());
                                            } else {
                                                interbankFeesMapper.updatePayStatus(id, finalTransferTime, DateUtil.now());
                                            }
                                        }
                                    }
                                } else {
                                    log.info("查询出一个季度中多条数据为:{}", JSON.toJSONString(res));
                                }
                            } else {
                                log.info("查询出cop的缴费记录为空");
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    try {
                        if (type == 1) {
                            InsuranceRegistrationFeesMapper insuranceRegistrationFeesMapper = SpringUtil.getBean(InsuranceRegistrationFeesMapper.class);
                            List<InsuranceRegistrationFees> insuranceRegistrationFees = insuranceRegistrationFeesMapper.selectList(ids);
                            if (CollectionUtil.isNotEmpty(insuranceRegistrationFees)) {
                                interBankFees = transformInsuranceRegistrationFeesServiceToInterBankFees(insuranceRegistrationFees);
                            }
                        } else {
                            interBankFees = interbankFeesMapper.selectList(ids);
                        }
                        List<JSONObject> preProductInfos = params.getList("productInfos", JSONObject.class);
                        if (type == 1) {
                            generateLogPostSyncStatusPrams(params, interBankFees, preProductInfos);
                            LogFYIUtils.postSyncPayStatusLog(params);
                        } else {
                            generateLogPostSyncStatusPrams(params, interBankFees, preProductInfos);
                            LogFYBUtils.postSyncPayStatusLog(params);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                isSyncPayStatus = false;
            });
        }
        return isSyncPayStatus ? "executing" : "completed";
    }

    private void generateLogPostSyncStatusPrams(JSONObject params, List<InterBankFees> interBankFees, List<JSONObject> preProductInfos) {
        for (InterBankFees interBankFee : interBankFees) {
            for (JSONObject jsonObject : preProductInfos) {
                String productId = interBankFee.getProductId();
                if (StringUtils.isNotBlank(productId) && jsonObject != null) {
                    if (productId.equals(jsonObject.getString("id"))) {
                        jsonObject.put("postPayStatus", interBankFee.getPaymentStatus());
                        break;
                    }
                }
            }
        }
        params.put("endTime", DateUtil.now());
        params.put("productInfos", JSON.toJSONString(preProductInfos));
        params.put("status", CommonStatus.SUCCESS.name());
    }

    private void generatePreSyncPayStatusLogParam(JSONObject params, List<InterBankFees> interBankFees, List<JSONObject> productInfos) {
        for (InterBankFees fees : interBankFees) {
            JSONObject info = new JSONObject();
            info.put("id", fees.getProductId());
            info.put("prePayStatus", fees.getPaymentStatus());
            productInfos.add(info);
        }
        params.put("productInfos", JSON.toJSONString(productInfos));
    }

    @Override
    public List<FileAndIds> generateInsuranceRegistrationFeesO32File(List<InsuranceRegistrationFees> fees, int flag, String initDate) {
        List<InterBankFees> interBankFees = transformInsuranceRegistrationFeesServiceToInterBankFees(fees);
        return generateO32File(interBankFees, flag, initDate);
    }

    @Override
    public boolean sendPaymentNotice(List<String> ids) {
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> jobIds = cronService.getJobIdByClass(InterbankFeesJob.class);
        List<InterBankFees> interBankFees = interbankFeesMapper.selectList(ids);
        if (CollectionUtil.isNotEmpty(interBankFees)) {
            List<String> sendIds = interBankFees.stream().map(InterBankFees::getId).collect(Collectors.toList());
            Map<String, String> params = new HashMap<>();
            for (InterBankFees interBankFee : interBankFees) {
                params.putIfAbsent(interBankFee.getProductId(), interBankFee.getFileId());
            }
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put("ids", sendIds);
            jobDataMap.put("productIdAndFileIdMap", params);
            jobDataMap.put("username", SecureUtil.currentUser().getAccount());
            cronService.startJobNow(jobIds, jobDataMap);
            return true;
        }
        return false;
    }

    @Override
    public List<InterBankFees> transformInsuranceRegistrationFeesServiceToInterBankFees(List<InsuranceRegistrationFees> insuranceRegistrationFees) {
        List<InterBankFees> interBankFees = new ArrayList<>();
        for (InsuranceRegistrationFees insuranceRegistrationFee : insuranceRegistrationFees) {
            InterBankFees fees = new InterBankFees();
            fees.setId(insuranceRegistrationFee.getId());
            fees.setPayMonth(insuranceRegistrationFee.getPayMonth());
            fees.setPaymentMethod(insuranceRegistrationFee.getPaymentMethod());
            fees.setProductId(insuranceRegistrationFee.getProductId());
            fees.setAmount(insuranceRegistrationFee.getAmount());
            fees.setFeeCollectionAgencies(insuranceRegistrationFee.getFeeCollectionAgencies());
            fees.setNameOfPayee(insuranceRegistrationFee.getNameOfPayee());
            fees.setBeneficiaryAccount(insuranceRegistrationFee.getBeneficiaryAccount());
            fees.setBeginCostDate(insuranceRegistrationFee.getBeginCostDate());
            fees.setEndCostDate(insuranceRegistrationFee.getEndCostDate());
            fees.setPaymentStatus(insuranceRegistrationFee.getPaymentStatus());
            interBankFees.add(fees);
        }
        return interBankFees;
    }

    @Override
    public List<InterBankFees> selectLastQuarterData(String quarterMonth, String quarterMonth1) {
        return interbankFeesMapper.selectLastQuarterData(quarterMonth, quarterMonth1);
    }


    /**
     * 执行rpa任务
     *
     * @param flowId 流程id
     * @param flag   0 代表银行间  1 代表中保登
     * @throws Exception 异常信息
     */
    @Override
    public void executeRPA(String flowId, List<String> ids, int flag, String dirPath, String uploadOrder, String type) throws Exception {
        FlowList flowList = SpringUtil.getBean(FlowListMapper.class).selectById(flowId);
        InsuranceRegistrationFeesMapper insuranceRegistrationFeesMapper = SpringUtil.getBean(InsuranceRegistrationFeesMapper.class);
        if (flowList == null) {
            BusinessException.throwException("流程为空");
        }
        BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
        RpaExecuteService rpaExecuteService = SpringUtil.getBean(RpaExecuteService.class);
        Date now = new Date();
        CronMapper cronMapper = SpringUtil.getBean(CronMapper.class);
        LambdaQueryWrapper<Cron> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 获取所有手动执行的任务
        List<Cron> manJobs = cronMapper.selectList(lambdaQueryWrapper);
        BaseCronLog baseCronLog = new BaseCronLog();
        String logId = IdUtil.getSnowflakeNextIdStr();
        baseCronLog.setId(logId);
        baseCronLog.setStartDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        baseCronLog.setDataDate(DateUtil.today());
        baseCronLog.setStatus(JobStatus.RUNNING);
        if (CollectionUtil.isNotEmpty(manJobs)) {
            Optional<Cron> first = manJobs.stream().filter(n -> null != n.getFlowId()).filter(n -> n.getFlowId().equals(flowList.getId())).findFirst();
            if (first.isPresent()) {
                baseCronLog.setTaskId(first.get().getJobId());
            } else {
                baseCronLog.setTaskId(IdUtil.getSnowflakeNextIdStr());
            }
        } else {
            baseCronLog.setTaskId(IdUtil.getSnowflakeNextIdStr());
        }
        baseCronLog.setExecutor(DEFAULT_USERNAME);
        baseCronLog.setExecuteMethod("MANUAL");
        baseCronLog.setDataDate(
                DateUtil.format(now, "yyyy年MM月dd日")
                        + "-" +
                        DateUtil.format(now, "yyyy年MM月dd日"));
        baseCronLogMapper.insert(baseCronLog);
        Map<String, Object> flowExtendParams = new HashMap<>();
        RpaExecLog rpaExecLog = null;
        boolean executeStatus = true;
        try {
            // 开启流程
            rpaExecLog = rpaExecuteService.executeRPA(flowList, flowExtendParams, logId);
            log.info("rpa执行日志为:{}", JSON.toJSONString(rpaExecLog));
            rpaExecuteService.startTimer(rpaExecLog, flowList, flowExtendParams, logId);
        } catch (Exception e) {
            e.printStackTrace();
            executeStatus = false;
        }
        // 状态变为 导入中
        if (flag == 0) {
            interbankFeesMapper.batchUpdateImportStatus(ids, ImportO32Status.IMPORTING.name(), DateUtil.now(), null);
        } else {
            insuranceRegistrationFeesMapper.batchUpdateImportStatus(ids, ImportO32Status.IMPORTING.name(), DateUtil.now(), null);
        }
        if (executeStatus) {
            // 同步等待rpa执行状态
            while (true) {
                BaseCronLog cronLog = baseCronLogMapper.selectById(logId);
                JobStatus rpaStatus = cronLog.getRpaStatus();
                if (rpaStatus == JobStatus.COMPLETE) {
                    log.info("此 {} RPA 任务已经完成", flowList.getShowName());
                    break;
                } else if (rpaStatus == JobStatus.FAILED) {
                    log.info("此 {} RPA 任务异常结束", flowList.getShowName());
                    BusinessException.throwException("RPA 任务异常结束");
                    break;
                }
                TimeUnit.SECONDS.sleep(5);
                log.info("正在检查 {} RPA 任务状态", flowList.getShowName());
            }
            BaseCronLog cronLog = baseCronLogMapper.selectById(logId);
            JobStatus rpaStatus = cronLog.getRpaStatus();
            log.info("rpa执行完成,其执行结果状态为:{}", rpaStatus.name());
            if (rpaStatus == JobStatus.COMPLETE) {
                if (rpaExecLog != null) {
                    RpaService rpaService = SpringUtil.getBean(RpaService.class);
                    Map<String, String> batchRpaChatParam = rpaService.getBatchRpaChatParam(rpaExecLog.getExecId(), DateUtil.today(), false);
                    String importRes = batchRpaChatParam.get("Param0Value_" + rpaExecLog.getExecId());
                    String handleRes = batchRpaChatParam.get("Param1Value_" + rpaExecLog.getExecId());
                    log.info("----------处理结果返回:{}", handleRes);
                    if (StringUtils.isNotBlank(importRes)) {
                        String regex = "(?=\\[\\d{2}:\\d{2}:\\d{2}])";
                        List<String> msg = CollectionUtil.newArrayList(importRes.split(regex));
                        log.info("拆分的日志信息为:{}", JSON.toJSONString(msg));
                        log.info("传递过来的ids = {}", JSON.toJSONString(ids));
                        if (CollectionUtil.isNotEmpty(msg)) {
                            List<JSONObject> indexList = new ArrayList<>();
                            Pattern pattern = Pattern.compile("第(\\d+)条记录");
                            for (String s : msg) {
                                Matcher matcher = pattern.matcher(s);
                                if (matcher.find()) {
                                    String numberStr = matcher.group(1);
                                    int number = Integer.parseInt(numberStr);
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("index", number - 1);
                                    jsonObject.put("error", s);
                                    indexList.add(jsonObject);
                                }
                            }
                            if (CollectionUtil.isEmpty(indexList)) {
                                // 说明全部成功
                                log.info("o32导入全部成功...");
                                if (flag == 0) {
                                    interbankFeesMapper.batchUpdateImportStatus(ids, ImportO32Status.IMPORT_SUCCESS.name(), null, null);
                                } else {
                                    insuranceRegistrationFeesMapper.batchUpdateImportStatus(ids, ImportO32Status.IMPORT_SUCCESS.name(), null, null);
                                }
                            } else {
                                List<Integer> failedIndex = indexList.stream().map(n -> Integer.parseInt(n.getString("index"))).collect(Collectors.toList());
                                List<Integer> successIndex = new ArrayList<>();
                                for (int i = 0; i < ids.size(); i++) {
                                    for (int index : failedIndex) {
                                        if (i != index) {
                                            successIndex.add(i);
                                        }
                                    }
                                }
                                if (CollectionUtil.isNotEmpty(successIndex)) {
                                    for (Integer index : successIndex) {
                                        String updateId = ids.get(index);
                                        log.info("id = {} 的记录导入成功", updateId);
                                        if (flag == 0) {
                                            interbankFeesMapper.updateImportStatus(updateId, ImportO32Status.IMPORT_SUCCESS.name(), null);
                                        } else {
                                            insuranceRegistrationFeesMapper.updateImportStatus(updateId, ImportO32Status.IMPORT_SUCCESS.name(), null);
                                        }
                                    }
                                }
                                for (JSONObject jsonObject : indexList) {
                                    try {
                                        int index = jsonObject.getInteger("index");
                                        String error = jsonObject.getString("error");
                                        List<String> errorMsg = CollectionUtil.newArrayList(error.split("原因:"));
                                        log.info("失败原因:{}", JSON.toJSONString(errorMsg));
                                        if (CollectionUtil.isNotEmpty(errorMsg)) {
                                            String errorStr = errorMsg.get(1);
                                            String updateId = ids.get(index);
                                            log.info("id = {} 的记录导入失败", updateId);
                                            if (flag == 0) {
                                                interbankFeesMapper.updateImportStatus(updateId, ImportO32Status.IMPORT_FAIL.name(), errorStr);
                                            } else {
                                                insuranceRegistrationFeesMapper.updateImportStatus(updateId, ImportO32Status.IMPORT_FAIL.name(), errorStr);
                                            }
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                                // 匹配处理结果
                                matchHandleResult(ids, flag, dirPath, uploadOrder, type);
                            }
                        }
                    }
                }
            } else {
                if (flag == 0) {
                    interbankFeesMapper.batchUpdateImportStatus(ids, ImportO32Status.IMPORT_FAIL.name(), null, "RPA导入失败");
                } else {
                    insuranceRegistrationFeesMapper.batchUpdateImportStatus(ids, ImportO32Status.IMPORT_FAIL.name(), null, "RPA导入失败");
                }
            }
        }
    }

    /**
     * 匹配处理结果
     * @param ids
     * @param flag
     */
    private void matchHandleResult(List<String> ids, int flag, String dirPath, String uploadOrder, String type) {
        log.info("需要获取处理结果文件的路径为:{}, 文件编号为:{}", dirPath, uploadOrder);
        if (StringUtils.isNotBlank(uploadOrder)) {
            List<String> fileNames = smbManager.listDir(dirPath);
            log.info("远程文件夹中的文件为:{}", JSON.toJSONString(fileNames));
            if (CollectionUtil.isNotEmpty(fileNames)) {
                for (String fileName : fileNames) {
                    try {
                        if (StringUtils.isNotBlank(fileName) && fileName.contains("处理结果") && fileName.contains(uploadOrder)) {
                            log.info("匹配到处理结果的文件为:{}", fileName);
                            byte[] downloadFile = smbManager.downloadFile(dirPath + File.separator + fileName);
                            log.info("文件下载成功准备开始解析");
                            ExcelReader reader = ExcelUtil.getReader(new ByteArrayInputStream(downloadFile), 0);
                            // 解析不同模板
                            if ("ASSET".equals(type)) {
                                resolveAssetTemplate(reader, ids, flag);
                            } else {
                                resolveNoninvestTemplate(reader, ids, flag);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    private void resolveNoninvestTemplate(ExcelReader reader, List<String> ids, int flag) {
        List<InterBankFees> interBankFees = new ArrayList<>();
        List<InsuranceRegistrationFees> insuranceRegistrationFees = new ArrayList<>();
        if (flag == 0) {
            interBankFees = interbankFeesMapper.selectList(ids);
        } else {
            insuranceRegistrationFees = SpringUtil.getBean(InsuranceRegistrationFeesMapper.class).selectList(ids);
        }
        List<O32AssetUnit> o32AssetUnits = o32AssetUnitMapper.selectList();
        // 记录列索引
        Map<String, Integer> indexMap = new HashMap<>();
        // 读取第一行表头
        List<Object> headers = reader.readRow(0);
        // 需要寻找列为 债券代码 债券简称 余额（元） 更新时间 持有人账号
        for (int i = 0; i < headers.size(); i++) {
            switch (String.valueOf(headers.get(i))) {
                case "基金名称":
                    indexMap.put("JJMC", i);
                    break;
                case "资产单元":
                    indexMap.put("ZCDY", i);
                case "金额":
                    indexMap.put("JE", i);
                    break;
            }
        }
        List<List<Object>> read = reader.read(1);
        for (List<Object> row : read) {
            // 基金名称
            Object o1 = row.get(indexMap.get("JJMC"));
            // 资产单元
            Object o2 = row.get(indexMap.get("ZCDY"));
            // 金额
            Object o3 = row.get(indexMap.get("JE"));
            String o1_name = String.valueOf(o1);
            String o2_name = String.valueOf(o2);
            String o3_name = String.valueOf(o3);
            if (flag == 0) {
                // 银行间
                if (CollectionUtil.isNotEmpty(interBankFees) && CollectionUtil.isNotEmpty(o32AssetUnits)) {
                    Map<String, List<O32AssetUnit>> collect = o32AssetUnits.stream().collect(Collectors.groupingBy(O32AssetUnit::getProductId));
                    for (InterBankFees fees : interBankFees) {
                        String productId = fees.getProductId();
                        List<O32AssetUnit> assetUnits = collect.get(productId);
                        if (CollectionUtil.isNotEmpty(assetUnits)) {
                            O32AssetUnit o32AssetUnit = assetUnits.get(0);
                            String o32FundName = o32AssetUnit.getO32FundName();
                            String o32AssetUnitName = o32AssetUnit.getO32AssetUnitName();
                            String amount = fees.getAmount();
                            if (StringUtils.isNotBlank(o32FundName) && o32FundName.equals(o1_name)
                                    && StringUtils.isNotBlank(o32AssetUnitName) && o32AssetUnitName.equals(o2_name)) {
                                if (StringUtils.isNotBlank(o3_name)) {
                                    o3_name = o3_name.replaceAll(",", "");
                                    if (o3_name.equals(amount)) {
                                        // 更新处理结果为完成
                                        log.info("更新银行间费用 = {} 的处理结果为成功", JSON.toJSONString(fees));
                                        interbankFeesMapper.updateHandleResult(fees.getId(), CommonStatus.SUCCESS.name());
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                // 中保登
                if (CollectionUtil.isNotEmpty(insuranceRegistrationFees) && CollectionUtil.isNotEmpty(o32AssetUnits)) {
                    Map<String, List<O32AssetUnit>> collect = o32AssetUnits.stream().collect(Collectors.groupingBy(O32AssetUnit::getProductId));
                    for (InsuranceRegistrationFees fees : insuranceRegistrationFees) {
                        String productId = fees.getProductId();
                        List<O32AssetUnit> assetUnits = collect.get(productId);
                        if (CollectionUtil.isNotEmpty(assetUnits)) {
                            O32AssetUnit o32AssetUnit = assetUnits.get(0);
                            String o32FundName = o32AssetUnit.getO32FundName();
                            String o32AssetUnitName = o32AssetUnit.getO32AssetUnitName();
                            String amount = fees.getAmount();
                            if (StringUtils.isNotBlank(o32FundName) && o32FundName.equals(o1_name)
                                    && StringUtils.isNotBlank(o32AssetUnitName) && o32AssetUnitName.equals(o2_name)) {
                                if (StringUtils.isNotBlank(o3_name)) {
                                    o3_name = o3_name.replaceAll(",", "");
                                    if (o3_name.equals(amount)) {
                                        // 更新处理结果为完成
                                        log.info("更新银行间费用 = {} 的处理结果为成功", JSON.toJSONString(fees));
                                        SpringUtil.getBean(InsuranceRegistrationFeesMapper.class).updateHandleResult(fees.getId(), CommonStatus.SUCCESS.name());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private void resolveAssetTemplate(ExcelReader reader, List<String> ids, int flag) {
        List<InsuranceRegistrationFees> insuranceRegistrationFees = new ArrayList<>();
        if (flag == 1) {
            insuranceRegistrationFees = SpringUtil.getBean(InsuranceRegistrationFeesMapper.class).selectList(ids);
        }
        List<O32AssetUnit> o32AssetUnits = o32AssetUnitMapper.selectList();
        // 记录列索引
        Map<String, Integer> indexMap = new HashMap<>();
        // 读取第一行表头
        List<Object> headers = reader.readRow(0);
        // 需要寻找列为 债券代码 债券简称 余额（元） 更新时间 持有人账号
        for (int i = 0; i < headers.size(); i++) {
            switch (String.valueOf(headers.get(i))) {
                case "基金名称":
                    indexMap.put("JJMC", i);
                    break;
                case "资产单元名称":
                    indexMap.put("ZCDYMC", i);
                case "发生金额":
                    indexMap.put("FSJE", i);
                    break;
            }
        }
        List<List<Object>> read = reader.read(1);
        for (List<Object> row : read) {
            // 基金名称
            Object o1 = row.get(indexMap.get("JJMC"));
            // 资产单元
            Object o2 = row.get(indexMap.get("ZCDYMC"));
            // 金额
            Object o3 = row.get(indexMap.get("FSJE"));
            String o1_name = String.valueOf(o1);
            String o2_name = String.valueOf(o2);
            String o3_name = String.valueOf(o3);
            if (flag == 1) {
                // 中保登
                if (CollectionUtil.isNotEmpty(insuranceRegistrationFees) && CollectionUtil.isNotEmpty(o32AssetUnits)) {
                    Map<String, List<O32AssetUnit>> collect = o32AssetUnits.stream().collect(Collectors.groupingBy(O32AssetUnit::getProductId));
                    for (InsuranceRegistrationFees fees : insuranceRegistrationFees) {
                        String productId = fees.getProductId();
                        List<O32AssetUnit> assetUnits = collect.get(productId);
                        if (CollectionUtil.isNotEmpty(assetUnits)) {
                            O32AssetUnit o32AssetUnit = assetUnits.get(0);
                            String o32FundName = o32AssetUnit.getO32FundName();
                            String o32AssetUnitName = o32AssetUnit.getO32AssetUnitName();
                            String amount = fees.getAmount();
                            if (StringUtils.isNotBlank(o32FundName) && o32FundName.equals(o1_name)
                                    && StringUtils.isNotBlank(o32AssetUnitName) && o32AssetUnitName.equals(o2_name)) {
                                if (StringUtils.isNotBlank(o3_name)) {
                                    o3_name = o3_name.replaceAll(",", "");
                                    if (o3_name.equals(amount)) {
                                        // 更新处理结果为完成
                                        log.info("更新中保登费用 = {} 的处理结果为成功", JSON.toJSONString(fees));
                                        SpringUtil.getBean(InsuranceRegistrationFeesMapper.class).updateHandleResult(fees.getId(), CommonStatus.SUCCESS.name());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 生成o32文件
     *
     * @param interbankFees 费用信息
     * @return 文件
     */
    @Override
    public List<FileAndIds> generateO32File(List<InterBankFees> interbankFees, int flag, String initDate) {
        log.info("查询出O32库中的日切日期为:{}", initDate);
        List<O32AssetUnit> o32AssetUnits = o32AssetUnitMapper.selectList();
        if (CollectionUtil.isEmpty(o32AssetUnits)) {
            BusinessException.throwException("没有获取到资产管理信息");
        }
        List<FileAndIds> res = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(interbankFees)) {
            if (flag == 1) {
                log.info("中保登都统一用 Noninvest 模板");
                if (CollectionUtil.isNotEmpty(interbankFees)) {
                    // 手工支付
                    log.info("生成o32文件时需要使用 Noninvest 模板");
                    FileAndIds fileAndIds = generateNoninvestFile(interbankFees, o32AssetUnits, initDate);
                    res.add(fileAndIds);
                }
            } else {
                List<InterBankFees> noninvestDatas = interbankFees.stream()
                        .filter(n -> StringUtils.isNotBlank(n.getPaymentMethod()))
                        .filter(n -> PaymentMethod.MANUAL_PAYMENT.name().equals(n.getPaymentMethod()))
                        .sorted(Comparator.comparing(InterBankFees::getId))
                        .collect(Collectors.toList());
                List<InterBankFees> assetDatas = interbankFees.stream()
                        .filter(n -> StringUtils.isNotBlank(n.getPaymentMethod()))
                        .filter(n -> PaymentMethod.AUTO_PAYMENT.name().equals(n.getPaymentMethod()))
                        .sorted(Comparator.comparing(InterBankFees::getId))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(noninvestDatas)) {
                    // 手工支付
                    log.info("生成o32文件时需要使用 Noninvest 模板");
                    FileAndIds fileAndIds = generateNoninvestFile(noninvestDatas, o32AssetUnits, initDate);
                    res.add(fileAndIds);
                }
                if (CollectionUtil.isNotEmpty(assetDatas)) {
                    // 自动扣收
                    log.info("生成o32文件时需要使用 Asset 模板");
                    FileAndIds fileAndIds = generateAssetFile(assetDatas, o32AssetUnits, initDate);
                    res.add(fileAndIds);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(res)) {
            List<String> ids = new ArrayList<>();
            for (FileAndIds fileAndIds : res) {
                ids.addAll(fileAndIds.getIds());
            }
            ids = ids.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ids)) {
                if (flag == 0) {
                    interbankFeesMapper.batchUpdateImportStatus(ids, ImportO32Status.GENERATED.name(), null, null);
                } else {
                    SpringUtil.getBean(InsuranceRegistrationFeesMapper.class).batchUpdateImportStatus(ids, ImportO32Status.GENERATED.name(), null, null);
                }
            }
        }
        return res;
    }

    @Override
    public List<InterBankFees> selectList(List<String> ids) {
        return interbankFeesMapper.selectList(ids);
    }

    @Override
    public FileAndIds generateNoninvestFile(List<InterBankFees> fees, List<O32AssetUnit> o32AssetUnits, String initDate) {
        FileAndIds fileAndIds = new FileAndIds();
        String orderNum = getOrderNum("Noninvest");
        fileAndIds.setUploadOrder(orderNum);
        log.info("获取到的Noninvest orderNum = {}", orderNum);
        Map<String, List<O32AssetUnit>> collect = o32AssetUnits.stream().collect(Collectors.groupingBy(O32AssetUnit::getProductId));
        String tradeDay = SpringUtil.getBean(MarketTradeDayService.class).getTradeDay(DateUtil.today(), "00", "1", 2);
        if (StringUtils.isNotBlank(tradeDay)) {
            tradeDay = tradeDay.replaceAll("-", "");
        }
        log.info("生成 Noninvest 文件时今日为:{}, 下2个交易所交易日为{}", DateUtil.today(), tradeDay);
        try (InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("import_to_o32/NONINVEST.xlsx")) {
            File dest = new File(importO32FilePath + "NONINVEST" + "_" + initDate + "_" + orderNum + ".xlsx");
            dest = FileUtil.copyFile(resourceAsStream, dest, StandardCopyOption.REPLACE_EXISTING);
            log.info("生成 Noninvest 文件名为:{}", FileUtil.getName(dest));
            try (ExcelReader reader = ExcelUtil.getReader(dest); ExcelWriter writer = reader.getWriter()) {
                // 需要填充 第 0 1 2 3 4 6 7 12 20
                // 先移除所有行
                List<Object> headers = reader.readRow(0);
                removeAllRows(reader, writer, dest);
                writer.writeHeadRow(headers);
                log.info("要写入excel中的行数为:{}", fees.size());
                for (InterBankFees interbankFees : fees) {
                    List<String> rowData = new ArrayList<>(Collections.nCopies(29, null));
                    String feeCollectionAgencies = interbankFees.getFeeCollectionAgencies();
                    String productId = interbankFees.getProductId();
                    List<O32AssetUnit> list = collect.get(productId);
                    if (CollectionUtil.isEmpty(list)) {
                        log.info("生成 Noninvest 文件时 未找到资产单元数据:{}", JSON.toJSONString(interbankFees));
                        continue;
                    }
                    switch (feeCollectionAgencies) {
                        // 0 业务类别序号
                        // 1 业务类别名称
                        // 20 入款/出款账号
                        case "上清":
                            rowData.set(0, "72509");
                            rowData.set(1, "上清所结算费支付");
                            rowData.set(20, "*************");
                            break;
                        case "中债":
                            rowData.set(0, "40142");
                            rowData.set(1, "中债登结算费支付");
                            rowData.set(20, "1109020812107022041010003");
                            break;
                        case "外汇":
                            rowData.set(0, "73369");
                            rowData.set(1, "外汇中心前台手续费");
                            rowData.set(20, "9558851001021844192");
                            break;
                        case "中保登":
                            rowData.set(0, "73838");
                            rowData.set(1, "中保登账户维护费支付");
                            rowData.set(20, "110060777018800067481");
                            break;
                    }
                    O32AssetUnit o32AssetUnit = list.get(0);
                    // 2 基金名称
                    rowData.set(2, o32AssetUnit.getO32FundName());
                    // 3 基金代码
                    rowData.set(3, productId);
                    // 4 资产单元名称
                    rowData.set(4, o32AssetUnit.getO32AssetUnitName());
                    // 6 币种代码
                    rowData.set(6, "CNY");
                    // 7 发生金额
                    String amount = interbankFees.getAmount();
                    rowData.set(7, StringUtils.isNotBlank(amount) ? amount.replaceAll(",", "") : "");
                    // 12 交收日期 文件生成日期 + 2个 交易所交易日
                    rowData.set(12, StringUtils.isNotBlank(tradeDay) ? tradeDay : "");
                    // 结转起始日期
                    String beginCostDate = interbankFees.getBeginCostDate();
                    if (StringUtils.isNotBlank(beginCostDate)) {
                        beginCostDate = beginCostDate + "-01";
                        LocalDate date = LocalDate.parse(beginCostDate);
                        rowData.set(26, LocalDateTimeUtil.format(date, "yyyyMMdd"));
                    }
                    // 结转截止日期
                    String endCostDate = interbankFees.getEndCostDate();
                    if (StringUtils.isNotBlank(endCostDate)) {
                        endCostDate = endCostDate + "-01";
                        LocalDate date = LocalDate.parse(endCostDate);
                        LocalDate finalDate = date.with(TemporalAdjusters.lastDayOfMonth());
                        rowData.set(27, LocalDateTimeUtil.format(finalDate, "yyyyMMdd"));
                    }
                    writer.writeRow(rowData, true);
                }
                writer.flush(dest);
            }
            fileAndIds.setFile(dest);
            fileAndIds.setIds(fees.stream().map(InterBankFees::getId).collect(Collectors.toList()));
            return fileAndIds;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 移除所有行
     * @param reader 读取流
     * @param writer 写入流
     * @param dest 目标文件
     */
    private void removeAllRows(ExcelReader reader, ExcelWriter writer, File dest) {
        Sheet sheet = reader.getSheet();
        int rowCount = sheet.getPhysicalNumberOfRows();
        for (int i = rowCount - 1; i >= 0; i--) {
            Row row = sheet.getRow(i);
            if (row != null) {
                sheet.removeRow(row);
            }
        }
        writer.flush(dest);
    }

    @Override
    public FileAndIds generateAssetFile(List<InterBankFees> fees, List<O32AssetUnit> o32AssetUnits, String initDate) {
        FileAndIds fileAndIds = new FileAndIds();
        String orderNum = getOrderNum("Asset");
        log.info("获取到的Asset orderNum = {}", orderNum);
        fileAndIds.setUploadOrder(orderNum);
        Map<String, List<O32AssetUnit>> collect = o32AssetUnits.stream().collect(Collectors.groupingBy(O32AssetUnit::getProductId));
        try (InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("import_to_o32/ASSET.xlsx")) {
            File dest = new File(importO32FilePath + "ASSET" + "_" + initDate + "_" + orderNum + ".xlsx");
            dest = FileUtil.copyFile(resourceAsStream, dest, StandardCopyOption.REPLACE_EXISTING);
            log.info("生成 Asset 文件名为:{}", FileUtil.getName(dest));
            try (ExcelReader reader = ExcelUtil.getReader(dest); ExcelWriter writer = reader.getWriter()) {
                // 需要填充 第 0 1 3 4 5 列
                log.info("要写入excel中的行数为:{}", fees.size());
                List<Object> headers = reader.readRow(0);
                removeAllRows(reader, writer, dest);
                writer.writeHeadRow(headers);
                for (InterBankFees interbankFees : fees) {
                    List<String> rowData = new ArrayList<>(Collections.nCopies(18, null));
                    String productId = interbankFees.getProductId();
                    List<O32AssetUnit> list = collect.get(productId);
                    if (CollectionUtil.isEmpty(list)) {
                        log.info("生成 Asset 文件时 未找到资产单元数据:{}", JSON.toJSONString(interbankFees));
                        continue;
                    }
                    O32AssetUnit o32AssetUnit = list.get(0);
                    // 0列 基金名称
                    rowData.set(0, o32AssetUnit.getO32FundName());
                    // 1列 资产单元名称
                    rowData.set(1, o32AssetUnit.getO32AssetUnitName());
                    // 3列 业务类型
                    rowData.set(3, "资金减少");
                    // 4列 发生金额
                    String amount = interbankFees.getAmount();
                    rowData.set(4, StringUtils.isNotBlank(amount) ? amount.replaceAll(",", "") : "");
                    // 5列 操作生效日期
                    // rowData.set(5, interbankFees.getPaymentDate());
                    rowData.set(5, DateUtil.format(new Date(), "yyyyMMdd"));
                    writer.writeRow(rowData, true);
                }
                writer.flush(dest);
                fileAndIds.setFile(dest);
                fileAndIds.setIds(fees.stream().map(InterBankFees::getId).sorted(Comparator.naturalOrder()).collect(Collectors.toList()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return fileAndIds;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    private synchronized String getOrderNum(String type) {
        String res = null;
        String today = DateUtil.today();
        String orderNum = interbankFeesMapper.getImportOrderNumber(today, type);
        try {
            if (StringUtils.isEmpty(orderNum)) {
                res = "1001";
                interbankFeesMapper.insertImportOrderNumber(today, res, type);
            } else {
                res = String.valueOf(Integer.parseInt(orderNum) + 1);
                interbankFeesMapper.updateImportOrderNumber(today, res, type);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    /**
     * 根据ocr结果生成银行间费用对象
     *
     * @param ocrResult ocr结果
     * @return 插入的数据
     */
    private List<InterBankFees> generateInterbankFees(OCRResult ocrResult, List<AccountInformation> accountInformationList) {
        List<InterBankFees> res = new ArrayList<>();
        // 根据ocr结果获取账套信息
        List<OCRProductInfo> list = getProductInfo(ocrResult, accountInformationList);
        log.info("获取的账套信息为:{}", JSONObject.toJSONString(list));
        if (CollectionUtil.isNotEmpty(list)) {
            for (OCRProductInfo ocrProductInfo : list) {
                InterBankFees interbankFees = new InterBankFees();
                String feeCollectionAgencies = ocrProductInfo.getFeeCollectionAgencies();
                AccountInformation accountInformation = ocrProductInfo.getAccountInformation();
                if (StringUtils.isBlank(feeCollectionAgencies)) {
                    log.error("没有找到收费机构,账套id为:{}", accountInformation.getId());
                    continue;
                }
                interbankFees.setId(IdUtil.getSnowflakeNextIdStr());
                if (accountInformation != null) {
                    interbankFees.setProductName(accountInformation.getFullProductName());
                    interbankFees.setProductId(accountInformation.getId());
                    switch (feeCollectionAgencies) {
                        case "上清":
                            interbankFees.setPaymentMethod(accountInformation.getPaymentMethodSQ());
                            break;
                        case "中债":
                            interbankFees.setPaymentMethod(accountInformation.getPaymentMethodZZ());
                            break;
                        case "外汇":
                            interbankFees.setPaymentMethod(accountInformation.getPaymentMethodWH());
                            break;
                    }
                }
                interbankFees.setFeeCollectionAgencies(feeCollectionAgencies);
                interbankFees.setOcrRecognizeStatus(OcrRecognizeStatusEnum.UNCONFIRMED.name());
                interbankFees.setImportO32Status(ImportO32Status.NO_GENERATE.name());
                interbankFees.setCode(ocrProductInfo.getCode());
                interbankFees.setName(ocrProductInfo.getName());
                interbankFees.setAmount(StringUtils.isNotBlank(ocrProductInfo.getAmount()) ? ocrProductInfo.getAmount().replaceAll(",", "") : "0.00");
                String amount = interbankFees.getAmount();
                Double amountDouble = Double.parseDouble(amount);
                if (Double.valueOf("0.00").equals(amountDouble)) {
                    interbankFees.setPaymentStatus(PaymentStatus.NO_PAID.name());
                } else {
                    interbankFees.setPaymentStatus(PaymentStatus.UNPAID.name());
                }
                // 读取其他信息
                getCommonDataFromOCRResult(interbankFees, ocrResult, feeCollectionAgencies);
                interbankFees.setOcrRecognizeStatus(OcrRecognizeStatusEnum.UNCONFIRMED.name());
                interbankFees.setMailSendStatus(MailStatus.UNSENT.name());
                res.add(interbankFees);
            }
        }
        return res;
    }

    /**
     * 读取ocr结果组装插入对象
     *
     * @param interbankFees         插入对象
     * @param ocrResult             ocr结果
     * @param feeCollectionAgencies 收费机构
     */
    private void getCommonDataFromOCRResult(InterBankFees interbankFees, OCRResult ocrResult, String feeCollectionAgencies) {
        switch (feeCollectionAgencies) {
            case "上清":
                readShangQingData(interbankFees, ocrResult);
                break;
            case "中债":
                readZhongZhaiData(interbankFees, ocrResult);
                break;
            case "外汇":
                readWaiHuiData(interbankFees, ocrResult);
                break;
        }
    }

    private void readWaiHuiData(InterBankFees interbankFees, OCRResult ocrResult) {
        Map<String, String> commonData = ocrResult.getCommonData();
        if (MapUtil.isNotEmpty(commonData)) {
            //收款人名称
            interbankFees.setNameOfPayee(commonData.get("户名"));
            //收款人账号
            interbankFees.setBeneficiaryAccount(commonData.get("账号"));
            //收款人开户行名称
            interbankFees.setBankAccount(commonData.get("开户行"));
            //收款人开户行号
            interbankFees.setBankAccountNumber(commonData.get("支付系统行号"));
            //摘要
            // 通知日期
            // 费用日期
            String costDateOcr = commonData.get("一、应付项目");
            Pattern pattern = Pattern.compile("\\d+");
            Matcher matcher = pattern.matcher(costDateOcr);
            List<Integer> dateList = new ArrayList<>();
            while (matcher.find()) {
                dateList.add(Integer.parseInt(matcher.group()));
            }
            log.info("识别出的外汇文件中的日期为:{}", dateList);
            if (CollectionUtil.isNotEmpty(dateList) && dateList.size() == 2) {
                Integer year = dateList.get(0);
                Integer quarter = dateList.get(1);
                LocalDate startOfQuarter = getStartOfQuarter(year, quarter);
                LocalDate endOfQuarter = getEndOfQuarter(year, quarter);
                interbankFees.setBeginCostDate(LocalDateTimeUtil.format(startOfQuarter, "yyyy-MM"));
                interbankFees.setEndCostDate(LocalDateTimeUtil.format(endOfQuarter, "yyyy-MM"));
                LocalDate localDate = endOfQuarter.plusMonths(1);
                interbankFees.setPayMonth(LocalDateTimeUtil.format(localDate, "yyyy-MM"));
            } else {
                setCostDate(interbankFees);
            }
        }
    }

    private void readZhongZhaiData(InterBankFees interbankFees, OCRResult ocrResult) {
        Map<String, String> commonData = ocrResult.getCommonData();
        if (MapUtil.isNotEmpty(commonData)) {
            //收款人名称
            interbankFees.setNameOfPayee(commonData.get("收款人"));
            //收款人账号
            interbankFees.setBeneficiaryAccount(commonData.get("账号"));
            //收款人开户行名称
            interbankFees.setBankAccount(commonData.get("开户银行"));
            //收款人开户行号
            interbankFees.setBankAccountNumber(commonData.get("开户银行行号"));
            //摘要
            interbankFees.setRemark(commonData.get("摘要"));
            // 通知日期
            // 费用日期
            String costDate = commonData.get("计费期");
            if (StringUtils.isNotBlank(costDate)) {
                List<Integer> dateList = new ArrayList<>();
                Pattern compile = Pattern.compile("\\d+");
                Matcher matcher = compile.matcher(costDate);
                while (matcher.find()) {
                    dateList.add(Integer.parseInt(matcher.group()));
                }
                log.info("识别到的中债的缴费日期为:{}", dateList);
                if (CollectionUtil.isNotEmpty(dateList) && dateList.size() == 6) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.set(dateList.get(0), dateList.get(1) - 1, dateList.get(2));
                    DateTime beginDateTime = DateTime.of(calendar.getTime());
                    calendar.set(dateList.get(3), dateList.get(4) - 1, dateList.get(5));
                    DateTime endDateTime = DateTime.of(calendar.getTime());
                    interbankFees.setBeginCostDate(DateUtil.format(beginDateTime, "yyyy-MM"));
                    interbankFees.setEndCostDate(DateUtil.format(endDateTime, "yyyy-MM"));
                    DateTime offset = DateUtil.offset(endDateTime, DateField.MONTH, 1);
                    interbankFees.setPayMonth(DateUtil.format(offset, "yyyy-MM"));
                } else {
                    String[] split = costDate.split("-");
                    if (split.length == 2) {
                        try {
                            DateTime beginDateTime = DateUtil.parse(split[0]);
                            DateTime endDateTime = DateUtil.parse(split[1]);
                            interbankFees.setBeginCostDate(DateUtil.format(beginDateTime, "yyyy-MM"));
                            interbankFees.setEndCostDate(DateUtil.format(endDateTime, "yyyy-MM"));
                            DateTime offset = DateUtil.offset(endDateTime, DateField.MONTH, 1);
                            interbankFees.setPayMonth(DateUtil.format(offset, "yyyy-MM"));
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            setCostDate(interbankFees);
                        }
                    } else {
                        setCostDate(interbankFees);
                    }
                }
            } else {
                setCostDate(interbankFees);
            }
        }
    }

    /**
     * 读取上清文件信息
     *
     * @param interbankFees 插入对象
     * @param ocrResult     ocr结果
     */
    private void readShangQingData(InterBankFees interbankFees, OCRResult ocrResult) {
        Map<String, String> commonData = ocrResult.getCommonData();
        if (MapUtil.isNotEmpty(commonData)) {
            //收款人名称
            interbankFees.setNameOfPayee(commonData.get("收款人名称"));
            //收款人账号
            interbankFees.setBeneficiaryAccount(commonData.get("收款人账号"));
            //收款人开户行名称
            interbankFees.setBankAccount(commonData.get("收款人开户行名称"));
            //收款人开户行号
            interbankFees.setBankAccountNumber(commonData.get("收款人开户行行号"));
            //摘要
            String costDateOcr = commonData.get("附言");
            interbankFees.setRemark(costDateOcr);
            // 通知日期
            // 费用日期
            try {
                Pattern pattern = Pattern.compile("\\d+");
                Matcher matcher = pattern.matcher(costDateOcr);
                List<Integer> dateList = new ArrayList<>();
                while (matcher.find()) {
                    dateList.add(Integer.parseInt(matcher.group()));
                }
                log.info("识别出上清的缴费日期信息为:{}", dateList);
                if (CollectionUtil.isNotEmpty(dateList) && dateList.size() == 3) {
                    Integer year = dateList.get(1);
                    Integer quarter = dateList.get(2);
                    LocalDate startOfQuarter = getStartOfQuarter(year, quarter);
                    LocalDate endOfQuarter = getEndOfQuarter(year, quarter);
                    interbankFees.setBeginCostDate(LocalDateTimeUtil.format(startOfQuarter, "yyyy-MM"));
                    interbankFees.setEndCostDate(LocalDateTimeUtil.format(endOfQuarter, "yyyy-MM"));
                    LocalDate localDate = endOfQuarter.plusMonths(1);
                    interbankFees.setPayMonth(LocalDateTimeUtil.format(localDate, "yyyy-MM"));
                } else {
                    setCostDate(interbankFees);
                }
            } catch (Exception e) {
                e.printStackTrace();
                setCostDate(interbankFees);
            }
        }
    }

    /**
     * 设置费用日期 默认为 当日所在季度的第一天和最后一天
     *
     * @param interbankFees 赋值对象
     */
    private void setCostDate(InterBankFees interbankFees) {
        LocalDate today = LocalDate.now();
        int currentQuarter = (today.getMonthValue() - 1) / 3 + 1;
        LocalDate startOfQuarter = getStartOfQuarter(today.getYear(), currentQuarter);
        LocalDate endOfQuarter = getEndOfQuarter(today.getYear(), currentQuarter);
        interbankFees.setBeginCostDate(LocalDateTimeUtil.format(startOfQuarter, "yyyy-MM"));
        interbankFees.setEndCostDate(LocalDateTimeUtil.format(endOfQuarter, "yyyy-MM"));
        LocalDate localDate = endOfQuarter.plusMonths(1);
        interbankFees.setPayMonth(LocalDateTimeUtil.format(localDate, "yyyy-MM"));
    }

    private LocalDate getStartOfQuarter(int year, int quarter) {
        switch (quarter) {
            case 1:
                return LocalDate.of(year, Month.JANUARY, 1);
            case 2:
                return LocalDate.of(year, Month.APRIL, 1);
            case 3:
                return LocalDate.of(year, Month.JULY, 1);
            case 4:
                return LocalDate.of(year, Month.OCTOBER, 1);
            default:
                throw new IllegalArgumentException("季度数无效");
        }
    }

    private LocalDate getEndOfQuarter(int year, int quarter) {
        switch (quarter) {
            case 1:
                return LocalDate.of(year, Month.MARCH, 31);
            case 2:
                return LocalDate.of(year, Month.JUNE, 30);
            case 3:
                return LocalDate.of(year, Month.SEPTEMBER, 30);
            case 4:
                return LocalDate.of(year, Month.DECEMBER, 31);
            default:
                throw new IllegalArgumentException("季度数无效");
        }
    }

    /**
     * 根据ocr结果获取账套信息
     *
     * @param ocrResult ocr结果
     * @return 账套信息
     */
    private List<OCRProductInfo> getProductInfo(OCRResult ocrResult, List<AccountInformation> accountInformationList) {
        List<OCRProductInfo> res = new ArrayList<>();
        List<Map<String, String>> tableData = ocrResult.getTableData();
        Map<String, String> commonData = ocrResult.getCommonData();
        if (CollectionUtil.isNotEmpty(tableData)) {
            // 上清文件中的 持有人账号 是放在 表格中的
            for (Map<String, String> map : tableData) {
                String holderAccount = map.get("持有人账号");
                String name = map.get("持有人简称");
                log.info("从上清文件中获取的持有人账号为:{}, 持有人简称:{}", holderAccount, name);
                if (StringUtils.isNotBlank(holderAccount)) {
                    Optional<AccountInformation> first = accountInformationList.stream().filter(n -> n.getClearingHouseHolderAccount().equals(holderAccount)).findFirst();
                    OCRProductInfo ocrProductInfo = new OCRProductInfo();
                    if (first.isPresent()) {
                        AccountInformation accountInformation = first.get();
                        ocrProductInfo.setAccountInformation(accountInformation);
                    }
                    ocrProductInfo.setFeeCollectionAgencies("上清");
                    String amount = map.get("应缴费用金额");
                    ocrProductInfo.setAmount(amount);
                    ocrProductInfo.setOcrInfo(JSONObject.toJSONString(map));
                    ocrProductInfo.setCode(holderAccount);
                    ocrProductInfo.setName(name);
                    res.add(ocrProductInfo);
                }
            }
        }
        if (MapUtil.isNotEmpty(commonData)) {
            String custodyAccount = commonData.get("托管账号");
            String name = commonData.get("账户全称");
            log.info("从中债文件中获取的托管账号为:{},账户全称为:{}", custodyAccount, name);
            if (StringUtils.isNotBlank(custodyAccount)) {
                Optional<AccountInformation> first = accountInformationList.stream().filter(n -> n.getCentralDebtAccountNumber().equals(custodyAccount)).findFirst();
                OCRProductInfo ocrProductInfo = new OCRProductInfo();
                if (first.isPresent()) {
                    AccountInformation accountInformation = first.get();
                    ocrProductInfo.setAccountInformation(accountInformation);
                }
                ocrProductInfo.setFeeCollectionAgencies("中债");
                ocrProductInfo.setCode(custodyAccount);
                ocrProductInfo.setName(name);
                ocrProductInfo.setAmount(commonData.get("应交费用合计(元)"));
                ocrProductInfo.setOcrInfo(JSONObject.toJSONString(commonData));
                res.add(ocrProductInfo);
            }
            String memberCode = commonData.get("会员代码");
            if (StringUtils.isNotBlank(memberCode)) {
                String memberName = commonData.get("机构名称");
                log.info("从外汇文件中获取的会员代码为:{}, 机构名称为:{}", memberCode, memberName);
                Optional<AccountInformation> first = accountInformationList.stream().filter(n -> n.getForeignExchangeCenterMemberCode().equals(memberCode)).findFirst();
                OCRProductInfo ocrProductInfo = new OCRProductInfo();
                if (first.isPresent()) {
                    AccountInformation accountInformation = first.get();
                    ocrProductInfo.setAccountInformation(accountInformation);
                }
                ocrProductInfo.setFeeCollectionAgencies("外汇");
                String ocrAmount = commonData.get("本季应付款项");
                if (StringUtils.isBlank(ocrAmount)) {
                    ocrAmount = commonData.get("优惠额本季应付款项");
                }
                ocrProductInfo.setAmount(ocrAmount);
                ocrProductInfo.setCode(memberCode);
                ocrProductInfo.setName(memberName);
                ocrProductInfo.setOcrInfo(JSONObject.toJSONString(commonData));
                res.add(ocrProductInfo);
            }
        }
        return res;
    }
}
